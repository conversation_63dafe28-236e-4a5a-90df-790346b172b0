# Gitea Actions 工作流程說明

本目錄包含用於自動執行 dailyBuildConfigs 目錄中批次檔的 Gitea Actions 工作流程。

## 工作流程文件

### `execute-all-scripts.yml`
自包含版本，避免外部依賴：
- 手動處理倉庫檢出，避免認證問題
- 分步執行每個批次檔
- 使用純 DOS 批次檔語法
- 本地歸檔執行結果

**觸發條件：**
- 推送到 main/master/develop 分支
- Pull Request 到 main/master/develop 分支
- 手動觸發

### `execute-all-scripts-simple.yml`
簡化版本，使用標準 checkout：
- 使用標準 `actions/checkout@v4`
- 分步執行每個批次檔
- 適用於認證配置正確的環境

**觸發條件：**
- 推送到 main/master/develop 分支
- Pull Request 到 main/master/develop 分支
- 手動觸發

## 當前 dailyBuildConfigs 目錄中的批次檔

1. **ideCleanBuild.bat** - 執行 TI CCS IDE 清理構建
2. **idebuild.bat** - 執行 TI CCS IDE 構建
3. **cppchk.bat** - 執行 Cppcheck 代碼靜態分析（需要構建後的項目資料）

## 主要特點

1. **DOS Command Line**：工作流程使用 DOS 批次檔語法執行，確保與 Windows 環境完全兼容
2. **動態腳本生成**：自動創建執行腳本來運行所有批次檔
3. **錯誤處理**：即使某個批次檔失敗，其他的仍會繼續執行
4. **詳細報告**：提供每個批次檔的執行狀態和退出代碼
5. **自動倉庫存取**：Gitea Actions 自動提供倉庫內容，無需外部依賴
6. **本地歸檔**：將執行結果歸檔到帶時間戳的目錄中
7. **簡潔高效**：最精簡的配置，執行速度快

## 選擇合適的工作流程

### 推薦使用順序

1. **首選**: `execute-all-scripts-simple.yml`
   - 如果您的 Gitea Actions 認證配置正確
   - 使用標準的 checkout action
   - 執行速度較快

2. **備選**: `execute-all-scripts.yml`
   - 如果遇到認證問題
   - 完全自包含，不依賴外部 actions
   - 手動處理倉庫檢出

## 使用方法

### 自動觸發
工作流程會在以下情況自動執行：
- 當您推送代碼到主要分支時
- 當您創建或更新 Pull Request 時

### 手動觸發
1. 進入 Gitea 倉庫頁面
2. 點擊 "Actions" 標籤
3. 選擇相應的工作流程
4. 點擊 "Run workflow" 按鈕

## 注意事項

### 環境要求
工作流程假設運行環境具有以下軟件：
- TI Code Composer Studio (CCS) 安裝在 `C:\ti\ccs2020\ccs\`
- Cppcheck 安裝在 `C:\Program Files\Cppcheck`

### 路徑配置
如果您的開發環境中 TI CCS 或 Cppcheck 安裝在不同位置，您需要：
1. 修改 dailyBuildConfigs 目錄中的批次檔路徑
2. 確保批次檔中的路徑與實際安裝位置一致

### 錯誤處理
- 工作流程使用 `continue-on-error: true` 確保即使某個步驟失敗，其他步驟仍會執行
- 所有執行結果都會上傳為 artifacts，方便後續分析
- 提供詳細的執行摘要和狀態報告
- 使用 DOS 批次檔的 `call` 命令確保正確執行子批次檔

## 工作流程執行

`execute-all-scripts.yml` 會自動執行以下步驟：

1. **自動倉庫存取**：Gitea Actions 自動提供倉庫內容
2. **驗證倉庫內容**：檢查當前工作目錄和文件
3. **動態生成執行腳本**：創建一個批次檔來執行所有 dailyBuildConfigs 中的腳本
4. **執行批次檔**：依序運行 ideCleanBuild.bat → idebuild.bat → cppchk.bat
5. **本地歸檔結果**：將執行日誌和構建產物歸檔到帶時間戳的目錄

## 自定義

### 添加新的批次檔
1. 將新的 .bat 文件放入 dailyBuildConfigs 目錄
2. 修改 `execute-all-scripts.yml` 中動態生成腳本的邏輯
3. 在生成腳本的部分添加新批次檔的執行命令

### 修改觸發條件
編輯 `execute-all-scripts.yml` 文件中的 `on:` 部分來修改觸發條件。

### 修改執行順序
修改 `execute-all-scripts.yml` 中動態生成腳本的順序，調整批次檔執行的先後順序。

## 故障排除

### 查看執行日誌
1. 進入 Actions 頁面
2. 點擊具體的工作流程執行
3. 查看各個步驟的詳細日誌

### 查看執行結果
執行完成後，結果會歸檔到帶時間戳的目錄中：
- `execution-logs-YYYYMMDD_HHMMSS/`: 包含所有執行日誌和結果
  - `cppcheck-result.txt`: Cppcheck 分析結果
  - `execute_all.bat`: 生成的執行腳本
  - `Debug/`: 構建產物目錄
  - `*.log`, `*.txt`: 其他日誌文件

### 常見問題
1. **TI CCS 路徑錯誤**: 檢查並更新批次檔中的 CCS 安裝路徑
2. **Cppcheck 未找到**: 確保 Cppcheck 已正確安裝
3. **權限問題**: 確保 Gitea Runner 有足夠權限執行批次檔
