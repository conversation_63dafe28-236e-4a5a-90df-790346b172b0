# Gitea Actions 工作流程說明

本目錄包含用於自動執行 dailyBuildConfigs 目錄中批次檔的 Gitea Actions 工作流程。

## 工作流程文件

### `execute-all-scripts.yml`
主要工作流程，執行完整的建置和分析流程：
- 使用 Gitea 專用的 checkout action
- 分步執行批次檔：ideImportPrj.bat → cppchk.bat
- 自動複製建置產物到共享資料夾
- 使用純 DOS 命令行語法

**觸發條件：**
- 推送到 main/master/develop 分支
- Pull Request 到 main/master/develop 分支
- 手動觸發（workflow_dispatch）

## 當前 dailyBuildConfigs 目錄中的批次檔

1. **ideImportPrj.bat** - 執行 TI CCS IDE 專案匯入
2. **cppchk.bat** - 執行 Cppcheck 代碼靜態分析（需要專案匯入後的資料）

## 工作流程執行步驟

當前 `execute-all-scripts.yml` 執行以下步驟：

1. **Checkout repository** - 檢出倉庫代碼
2. **Execute ideImportPrj.bat** - 執行專案匯入
3. **Execute cppchk.bat** - 執行代碼靜態分析
4. **Copy .out files to shared folder** - 複製建置產物到共享資料夾

## 主要特點

1. **DOS Command Line**：工作流程使用 DOS 批次檔語法執行，確保與 Windows 環境完全兼容
2. **Gitea 專用認證**：使用 GIT_ACCESS_TOKEN 進行 Gitea Actions 認證
3. **錯誤處理**：即使某個批次檔失敗，其他的仍會繼續執行
4. **詳細報告**：提供每個批次檔的執行狀態和退出代碼
5. **自動檔案複製**：自動將 .out 檔案複製到指定的共享資料夾
6. **路徑檢查**：執行前檢查源檔案是否存在，提供詳細的除錯資訊
7. **簡潔高效**：精簡的配置，專注於核心建置流程

## 檔案複製功能

### 自動複製建置產物
工作流程會自動將建置產物複製到指定位置：

**源路徑**: `C:\theiaIde\workspace\bq769x2_TIDA010247_LP_MSPM0G3519_nortos_ticlang\Debug\*.out`
**目標路徑**: `O:\嘉普-研發共享資料夾\WBS\跨部門專案\TPE-025\Firmware\`

### 複製流程
1. 檢查源目錄是否存在 .out 檔案
2. 如果檔案存在，複製到指定的共享資料夾
3. 驗證複製結果並列出目標資料夾中的檔案
4. 提供詳細的成功/失敗訊息

## 使用方法

### 自動觸發
工作流程會在以下情況自動執行：
- 當您推送代碼到主要分支時
- 當您創建或更新 Pull Request 時

### 手動觸發
1. 進入 Gitea 倉庫頁面
2. 點擊 "Actions" 標籤
3. 選擇相應的工作流程
4. 點擊 "Run workflow" 按鈕

## 注意事項

### 環境要求
工作流程假設運行環境具有以下軟件和配置：
- TI Code Composer Studio (CCS) 已正確安裝和配置
- Cppcheck 已正確安裝和配置
- 網路磁碟機 O: 已掛載並可存取
- Gitea Actions runner 有權限存取共享資料夾路徑

### 路徑配置
重要路徑配置：
1. **源路徑**: `C:\theiaIde\workspace\bq769x2_TIDA010247_LP_MSPM0G3519_nortos_ticlang\Debug\`
2. **目標路徑**: `O:\嘉普-研發共享資料夾\WBS\跨部門專案\TPE-025\Firmware\`
3. **批次檔路徑**: `dailyBuildConfigs\` 目錄中的 .bat 檔案

如果路徑不同，需要修改：
- dailyBuildConfigs 目錄中的批次檔路徑
- workflow 中的源路徑和目標路徑

### 錯誤處理
- 工作流程使用 `continue-on-error: true` 確保即使某個步驟失敗，其他步驟仍會執行
- 提供詳細的執行摘要和狀態報告
- 使用 DOS 批次檔的 `call` 命令確保正確執行子批次檔
- 檔案複製前會檢查源檔案是否存在，避免不必要的錯誤
- 複製操作包含詳細的成功/失敗訊息和檔案列表

## 自定義

### 添加新的批次檔
1. 將新的 .bat 文件放入 dailyBuildConfigs 目錄
2. 在 `execute-all-scripts.yml` 中添加新的執行步驟
3. 參考現有步驟的格式，確保錯誤處理和日誌記錄

### 修改觸發條件
編輯 `execute-all-scripts.yml` 文件中的 `on:` 部分來修改觸發條件。

### 修改複製目標
如需修改檔案複製的目標路徑：
1. 編輯 `execute-all-scripts.yml` 中的 "Copy .out files to shared folder" 步驟
2. 更新目標路徑為新的共享資料夾位置
3. 確保 Gitea Actions runner 有權限存取新路徑

## 故障排除

### 查看執行日誌
1. 進入 Actions 頁面
2. 點擊具體的工作流程執行
3. 查看各個步驟的詳細日誌

### 查看執行結果
執行完成後，可以通過以下方式查看結果：
- **Gitea Actions 日誌**: 在 Actions 頁面查看詳細的執行日誌
- **共享資料夾**: 檢查 `O:\嘉普-研發共享資料夾\WBS\跨部門專案\TPE-025\Firmware\` 中的 .out 檔案
- **源目錄**: 檢查 `C:\theiaIde\workspace\bq769x2_TIDA010247_LP_MSPM0G3519_nortos_ticlang\Debug\` 中的建置產物

### 常見問題
1. **TI CCS 路徑錯誤**: 檢查並更新批次檔中的 CCS 安裝路徑
2. **Cppcheck 未找到**: 確保 Cppcheck 已正確安裝
3. **權限問題**: 確保 Gitea Runner 有足夠權限執行批次檔
4. **網路路徑存取失敗**: 確認 O: 磁碟機已正確掛載且有寫入權限
5. **檔案複製失敗**: 檢查源路徑是否存在 .out 檔案，確認目標資料夾可寫入
6. **認證問題**: 確保 GIT_ACCESS_TOKEN 已正確設定在 Gitea secrets 中

## 技術細節

### 使用的 Gitea Actions
- **Checkout**: `http://${{ secrets.GIT_ACCESS_TOKEN }}:@10.102.30.36:3000/actions/checkout@v4`
- **Shell**: DOS Command Line (`cmd`)
- **認證**: 使用 `GIT_ACCESS_TOKEN` secret 進行認證

### 檔案路徑說明
- **工作目錄**: Gitea Actions 的 checkout workspace
- **源建置目錄**: `C:\theiaIde\workspace\bq769x2_TIDA010247_LP_MSPM0G3519_nortos_ticlang\Debug\`
- **目標共享資料夾**: `O:\嘉普-研發共享資料夾\WBS\跨部門專案\TPE-025\Firmware\`
