@echo off
echo =========================================
echo Project Reorganization Script
echo =========================================
echo This script will:
echo 1. Create MSPM0G3519_BMS_APP and MSPM0G3519_BMS_FBL subdirectories
echo 2. Move ALL files and directories to MSPM0G3519_BMS_APP
echo 3. Preserve dailyBuildConfigs .gitea .gitignore at root level
echo 4. Update .gitignore with directory prefixes
echo 5. Preserve all commit history
echo 6. Use general rules instead of specific file names
echo =========================================
echo.

pause

echo Step 1: Creating subdirectories...
mkdir MSPM0G3519_BMS_APP 2>nul
mkdir MSPM0G3519_BMS_FBL 2>nul
echo - MSPM0G3519_BMS_APP directory created
echo - MSPM0G3519_BMS_FBL directory created
echo.

echo Step 2: Moving all files and directories to MSPM0G3519_BMS_APP...

REM Move all visible files to MSPM0G3519_BMS_APP
for %%f in (*) do (
    if not "%%f"=="MSPM0G3519_BMS_APP" (
        if not "%%f"=="MSPM0G3519_BMS_FBL" (
            if not "%%f"=="dailyBuildConfigs" (
                if not "%%f"=="reorganize-project.bat" (
                    if not "%%f"=="reorganize-project-clean.bat" (
                        echo Moving %%f to MSPM0G3519_BMS_APP/
                        git mv "%%f" MSPM0G3519_BMS_APP/ 2>nul
                    )
                )
            )
        )
    )
)

REM Move hidden files except .gitea .gitignore .git
for /f "delims=" %%f in ('dir /b /a:h .* 2^>nul') do (
    if not "%%f"==".gitea" (
        if not "%%f"==".gitignore" (
            if not "%%f"==".git" (
                echo Moving hidden file %%f to MSPM0G3519_BMS_APP/
                git add -f "%%f" 2>nul
                git mv "%%f" MSPM0G3519_BMS_APP/ 2>nul
            )
        )
    )
)

echo - All applicable files moved to MSPM0G3519_BMS_APP/
echo.

echo Step 3: Updating .gitignore with directory prefixes...
echo # MSPM0G3519_BMS Application directory ignores > .gitignore
echo MSPM0G3519_BMS_APP/**/.settings/ >> .gitignore
echo MSPM0G3519_BMS_APP/Debug/ >> .gitignore
echo. >> .gitignore
echo # MSPM0G3519_BMS Flash Boot Loader directory ignores >> .gitignore
echo MSPM0G3519_BMS_FBL/**/.settings/ >> .gitignore
echo MSPM0G3519_BMS_FBL/Debug/ >> .gitignore
echo - .gitignore updated with directory prefixes
echo.

echo Step 4: Adding all changes to git...
git add .
echo - All changes staged
echo.

echo Step 5: Committing changes...
git commit -m "Reorganize: Move all files to MSPM0G3519_BMS_APP subdirectory using general rules"
echo - Changes committed with preserved history
echo.

echo =========================================
echo Project reorganization completed successfully!
echo =========================================
echo.
echo Final structure:
echo   Root/
echo   - .gitea/                        CI/CD workflows
echo   - dailyBuildConfigs/             Build scripts
echo   - MSPM0G3519_BMS_APP/            Application files
echo   - MSPM0G3519_BMS_FBL/            Flash Boot Loader files
echo   - .gitignore                     Updated with prefixes
echo.
echo All commit history has been preserved.
echo You can now add flash boot loader files to the MSPM0G3519_BMS_FBL/ directory.
echo.
pause
