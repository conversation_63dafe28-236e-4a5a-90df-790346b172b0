name: Execute All Daily Build Scripts

on:
  push:
    branches: [ main, master, develop ]
  pull_request:
    branches: [ main, master, develop ]
  workflow_dispatch:
    # Allow manual trigger from Gitea UI

jobs:
  execute-scripts:
    runs-on: host
    
    steps:
    - name: Checkout repository
      uses: http://${{ secrets.GIT_ACCESS_TOKEN }}:@************:3000/actions/checkout@v4

    - name: Execute ideImportPrj.bat
      run: |
        echo Executing ideImportPrj.bat...
        if exist "dailyBuildConfigs\ideImportPrj.bat" (
          call dailyBuildConfigs\ideImportPrj.bat
          if errorlevel 1 (
            echo [FAILED] ideImportPrj.bat completed with errors
          ) else (
            echo [SUCCESS] ideImportPrj.bat completed successfully
          )
        ) else (
          echo Error: ideImportPrj.bat not found
        )
        echo.
      shell: cmd
      #continue-on-error: true
      
    # - name: Execute ideCleanBuild.bat
    #   run: |
    #     echo Executing ideCleanBuild.bat...
    #     cd dailyBuildConfigs
    #     if exist "ideCleanBuild.bat" (
    #       echo =========================================
    #       echo Executing: ideCleanBuild.bat
    #       echo =========================================
    #       call ideCleanBuild.bat
    #       if errorlevel 1 (
    #         echo [FAILED] ideCleanBuild.bat completed with errors
    #       ) else (
    #         echo [SUCCESS] ideCleanBuild.bat completed successfully
    #       )
    #     ) else (
    #       echo Error: ideCleanBuild.bat not found
    #     )
    #     echo.
    #   shell: cmd
    #   #continue-on-error: true
      
    # - name: Execute idebuild.bat
    #   run: |
    #     echo Executing idebuild.bat...
    #     if exist "dailyBuildConfigs\idebuild.bat" (
    #       echo =========================================
    #       echo Executing: idebuild.bat
    #       echo =========================================
    #       call dailyBuildConfigs\idebuild.bat
    #       if errorlevel 1 (
    #         echo [FAILED] idebuild.bat completed with errors
    #       ) else (
    #         echo [SUCCESS] idebuild.bat completed successfully
    #       )
    #     ) else (
    #       echo Error: idebuild.bat not found
    #     )
    #     echo.
    #   shell: cmd
    #   #continue-on-error: true
      
    - name: Execute cppchk.bat
      run: |
        echo Executing cppchk.bat...
        if exist "dailyBuildConfigs\cppchk.bat" (
          call dailyBuildConfigs\cppchk.bat
          if errorlevel 1 (
            echo [FAILED] cppchk.bat completed with errors
          ) else (
            echo [SUCCESS] cppchk.bat completed successfully
          )
        ) else (
          echo Error: cppchk.bat not found
        )
        echo.
      shell: cmd
      #continue-on-error: true

    - name: Copy .out files to Debug directory
      run: |
        echo Creating Debug directory and copying .out files...
        mkdir Debug 2>nul

        echo Checking source directory for .out files...
        if exist "C:\theiaIde\workspace\bq769x2_TIDA010247_LP_MSPM0G3519_nortos_ticlang\Debug\*.out" (
          echo Found .out files, copying to Debug directory...
          copy "C:\theiaIde\workspace\bq769x2_TIDA010247_LP_MSPM0G3519_nortos_ticlang\Debug\*.out" Debug\
          if errorlevel 1 (
            echo [FAILED] Failed to copy .out files
          ) else (
            echo [SUCCESS] .out files copied successfully
          )
        ) else (
          echo [WARNING] No .out files found in source directory
        )

        echo Listing copied files in Debug directory...
        if exist "Debug\*.out" (
          dir Debug\*.out /b
        ) else (
          echo No .out files found in Debug directory
        )
        echo.
      shell: cmd
      #continue-on-error: true

    - name: Upload Debug artifacts
      uses: http://${{ secrets.GIT_ACCESS_TOKEN }}:@************:3000/hori_liu/upload-artifact@main
      if: always()
      with:
        name: debug-artifacts
        path: Debug/
        retention-days: 7
        if-no-files-found: warn
