name: Execute All Daily Build Scripts

on:
  push:
    branches: [ main, master, develop ]
  pull_request:
    branches: [ main, master, develop ]
  workflow_dispatch:
    # Allow manual trigger from Gitea UI

jobs:
  execute-scripts:
    runs-on: host
    
    steps:
    - name: Checkout repository
      uses: http://${{ secrets.GIT_ACCESS_TOKEN }}:@************:3000/actions/checkout@v4

    - name: Execute ideImportPrj.bat
      run: |
        echo Executing ideImportPrj.bat...
        if exist "dailyBuildConfigs\ideImportPrj.bat" (
          call dailyBuildConfigs\ideImportPrj.bat
          if errorlevel 1 (
            echo [FAILED] ideImportPrj.bat completed with errors
          ) else (
            echo [SUCCESS] ideImportPrj.bat completed successfully
          )
        ) else (
          echo Error: ideImportPrj.bat not found
        )
        echo.
      shell: cmd
      #continue-on-error: true
      
    # - name: Execute ideCleanBuild.bat
    #   run: |
    #     echo Executing ideCleanBuild.bat...
    #     cd dailyBuildConfigs
    #     if exist "ideCleanBuild.bat" (
    #       echo =========================================
    #       echo Executing: ideCleanBuild.bat
    #       echo =========================================
    #       call ideCleanBuild.bat
    #       if errorlevel 1 (
    #         echo [FAILED] ideCleanBuild.bat completed with errors
    #       ) else (
    #         echo [SUCCESS] ideCleanBuild.bat completed successfully
    #       )
    #     ) else (
    #       echo Error: ideCleanBuild.bat not found
    #     )
    #     echo.
    #   shell: cmd
    #   #continue-on-error: true
      
    # - name: Execute idebuild.bat
    #   run: |
    #     echo Executing idebuild.bat...
    #     if exist "dailyBuildConfigs\idebuild.bat" (
    #       echo =========================================
    #       echo Executing: idebuild.bat
    #       echo =========================================
    #       call dailyBuildConfigs\idebuild.bat
    #       if errorlevel 1 (
    #         echo [FAILED] idebuild.bat completed with errors
    #       ) else (
    #         echo [SUCCESS] idebuild.bat completed successfully
    #       )
    #     ) else (
    #       echo Error: idebuild.bat not found
    #     )
    #     echo.
    #   shell: cmd
    #   #continue-on-error: true
      
    - name: Execute cppchk.bat
      run: |
        echo Executing cppchk.bat...
        if exist "dailyBuildConfigs\cppchk.bat" (
          call dailyBuildConfigs\cppchk.bat
          if errorlevel 1 (
            echo [FAILED] cppchk.bat completed with errors
          ) else (
            echo [SUCCESS] cppchk.bat completed successfully
          )
        ) else (
          echo Error: cppchk.bat not found
        )
        echo.
      shell: cmd
      #continue-on-error: true

    - name: Copy .out files to shared folder
      run: |
        echo Copying .out files to shared folder...
        echo Checking source directory for .out files...
        if exist "C:\theiaIde\workspace\MSPM0G3519_BMS_APP\Debug\*.out" (
          echo Found .out files, copying to X:\UploadToCloud\HighPower\Hori_liu...
          copy "C:\theiaIde\workspace\MSPM0G3519_BMS_APP\Debug\*.out" "X:\UploadToCloud\HighPower\Hori_liu\"
          if errorlevel 1 (
            echo [FAILED] Failed to copy .out files to shared folder
          ) else (
            echo [SUCCESS] .out files copied successfully to shared folder
          )
        ) else (
          echo [WARNING] No .out files found in source directory
        )

        echo Listing files in destination folder...
        if exist "X:\UploadToCloud\HighPower\Hori_liu\*.out" (
          dir "X:\UploadToCloud\HighPower\Hori_liu\*.out" /b
        ) else (
          echo No .out files found in destination folder
        )
        echo.
      shell: cmd
      #continue-on-error: true
