name: Execute All Daily Build Scripts

on:
  push:
    branches: [ main, master, develop ]
  pull_request:
    branches: [ main, master, develop ]
  workflow_dispatch:
    # Allow manual trigger from Gitea UI

jobs:
  execute-scripts:
    runs-on: host
    
    steps:
    - name: Checkout repository
      uses: http://${{ secrets.GIT_ACCESS_TOKEN }}:@************:3000/actions/checkout@v4

    - name: Execute ideImportPrj.bat
      run: |
        echo Executing ideImportPrj.bat...
        if exist "dailyBuildConfigs\ideImportPrj.bat" (
          call dailyBuildConfigs\ideImportPrj.bat
          if errorlevel 1 (
            echo [FAILED] ideImportPrj.bat completed with errors
          ) else (
            echo [SUCCESS] ideImportPrj.bat completed successfully
          )
        ) else (
          echo Error: ideImportPrj.bat not found
        )
        echo.
      shell: cmd
      #continue-on-error: true
      
    # - name: Execute ideCleanBuild.bat
    #   run: |
    #     echo Executing ideCleanBuild.bat...
    #     cd dailyBuildConfigs
    #     if exist "ideCleanBuild.bat" (
    #       echo =========================================
    #       echo Executing: ideCleanBuild.bat
    #       echo =========================================
    #       call ideCleanBuild.bat
    #       if errorlevel 1 (
    #         echo [FAILED] ideCleanBuild.bat completed with errors
    #       ) else (
    #         echo [SUCCESS] ideCleanBuild.bat completed successfully
    #       )
    #     ) else (
    #       echo Error: ideCleanBuild.bat not found
    #     )
    #     echo.
    #   shell: cmd
    #   #continue-on-error: true
      
    # - name: Execute idebuild.bat
    #   run: |
    #     echo Executing idebuild.bat...
    #     if exist "dailyBuildConfigs\idebuild.bat" (
    #       echo =========================================
    #       echo Executing: idebuild.bat
    #       echo =========================================
    #       call dailyBuildConfigs\idebuild.bat
    #       if errorlevel 1 (
    #         echo [FAILED] idebuild.bat completed with errors
    #       ) else (
    #         echo [SUCCESS] idebuild.bat completed successfully
    #       )
    #     ) else (
    #       echo Error: idebuild.bat not found
    #     )
    #     echo.
    #   shell: cmd
    #   #continue-on-error: true
      
    - name: Execute cppchk.bat
      run: |
        echo Executing cppchk.bat...
        if exist "dailyBuildConfigs\cppchk.bat" (
          call dailyBuildConfigs\cppchk.bat
          if errorlevel 1 (
            echo [FAILED] cppchk.bat completed with errors
          ) else (
            echo [SUCCESS] cppchk.bat completed successfully
          )

          echo Creating Debug directory and copying files...
          mkdir Debug 2>nul

          echo Checking source directory...
          if exist "C:\theiaIde\workspace\bq769x2_TIDA010247_LP_MSPM0G3519_nortos_ticlang\Debug" (
            echo Source Debug directory exists
            dir "C:\theiaIde\workspace\bq769x2_TIDA010247_LP_MSPM0G3519_nortos_ticlang\Debug" /b
          ) else (
            echo WARNING: Source Debug directory does not exist
          )

          echo Copying *.map files...
          if exist "C:\theiaIde\workspace\bq769x2_TIDA010247_LP_MSPM0G3519_nortos_ticlang\Debug\*.map" (
            copy "C:\theiaIde\workspace\bq769x2_TIDA010247_LP_MSPM0G3519_nortos_ticlang\Debug\*.map" ${{ gitea.workspace }}\Debug\
            echo Map files copied successfully
          ) else (
            echo No .map files found in source directory
          )

          echo Copying *.out files...
          if exist "C:\theiaIde\workspace\bq769x2_TIDA010247_LP_MSPM0G3519_nortos_ticlang\Debug\*.out" (
            copy "C:\theiaIde\workspace\bq769x2_TIDA010247_LP_MSPM0G3519_nortos_ticlang\Debug\*.out" ${{ gitea.workspace }}\Debug\
            echo Out files copied successfully
          ) else (
            echo No .out files found in source directory
          )

          echo Creating .clangd directory and copying all files...
          mkdir Debug\.clangd 2>nul
          if exist "C:\theiaIde\workspace\bq769x2_TIDA010247_LP_MSPM0G3519_nortos_ticlang\Debug\.clangd" (
            echo .clangd directory exists in source
            copy "C:\theiaIde\workspace\bq769x2_TIDA010247_LP_MSPM0G3519_nortos_ticlang\Debug\.clangd\*.*" ${{ gitea.workspace }}\Debug\.clangd\
            dir ${{ gitea.workspace }}\Debug\.clangd /b
            echo .clangd files copied successfully
          ) else (
            echo No .clangd directory found in source
          )

          echo Checking copied files in Debug directory...
          if exist "Debug" (
            echo Contents of Debug directory:
            dir Debug /s /b
          ) else (
            echo Debug directory was not created
          )

          echo File copy operations completed.
        ) else (
          echo Error: cppchk.bat not found
        )
        echo.
      shell: cmd
      #continue-on-error: true
      
    - name: Upload execution logs
      uses: http://${{ secrets.GIT_ACCESS_TOKEN }}:@************:3000/hori_liu/upload-artifact@main
      if: always()
      with:
        name: execution-logs
        path: Debug/
        retention-days: 7
        if-no-files-found: warn
