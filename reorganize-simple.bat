@echo off
setlocal enabledelayedexpansion

echo =========================================
echo Project Reorganization Script
echo =========================================
echo.

pause

echo Creating directories...
if not exist MSPM0G3519_BMS_APP mkdir MSPM0G3519_BMS_APP
if not exist MSPM0G3519_BMS_FBL mkdir MSPM0G3519_BMS_FBL
echo Directories created.
echo.

echo Moving files to MSPM0G3519_BMS_APP...

REM Move specific files that we know exist
if exist "*.c" (
    for %%f in (*.c) do (
        echo Moving %%f
        git mv "%%f" MSPM0G3519_BMS_APP\ 2>nul
    )
)

if exist "*.h" (
    for %%f in (*.h) do (
        echo Moving %%f
        git mv "%%f" MSPM0G3519_BMS_APP\ 2>nul
    )
)

if exist "*.syscfg" (
    for %%f in (*.syscfg) do (
        echo Moving %%f
        git mv "%%f" MSPM0G3519_BMS_APP\ 2>nul
    )
)

if exist "*.html" (
    for %%f in (*.html) do (
        echo Moving %%f
        git mv "%%f" MSPM0G3519_BMS_APP\ 2>nul
    )
)

if exist "*.md" (
    for %%f in (*.md) do (
        if not "%%f"=="README.md" (
            echo Moving %%f
            git mv "%%f" MSPM0G3519_BMS_APP\ 2>nul
        )
    )
)

REM Move directories
for /d %%d in (*) do (
    if not "%%d"=="MSPM0G3519_BMS_APP" (
        if not "%%d"=="MSPM0G3519_BMS_FBL" (
            if not "%%d"=="dailyBuildConfigs" (
                if not "%%d"==".gitea" (
                    echo Moving directory %%d
                    git mv "%%d" MSPM0G3519_BMS_APP\ 2>nul
                )
            )
        )
    )
)

REM Move hidden files
if exist ".ccsproject" git mv ".ccsproject" MSPM0G3519_BMS_APP\ 2>nul
if exist ".clangd" git mv ".clangd" MSPM0G3519_BMS_APP\ 2>nul
if exist ".cproject" git mv ".cproject" MSPM0G3519_BMS_APP\ 2>nul
if exist ".project" git mv ".project" MSPM0G3519_BMS_APP\ 2>nul
if exist ".settings" (
    git add -f ".settings" 2>nul
    git mv ".settings" MSPM0G3519_BMS_APP\ 2>nul
)

echo File moving completed.
echo.

echo Updating .gitignore...
echo # MSPM0G3519_BMS Application directory ignores > .gitignore
echo MSPM0G3519_BMS_APP/**/.settings/ >> .gitignore
echo MSPM0G3519_BMS_APP/Debug/ >> .gitignore
echo. >> .gitignore
echo # MSPM0G3519_BMS Flash Boot Loader directory ignores >> .gitignore
echo MSPM0G3519_BMS_FBL/**/.settings/ >> .gitignore
echo MSPM0G3519_BMS_FBL/Debug/ >> .gitignore
echo .gitignore updated.
echo.

echo Staging and committing changes...
git add .
git commit -m "Reorganize project structure to subdirectories"
echo Changes committed.
echo.

echo =========================================
echo Reorganization completed successfully!
echo =========================================
pause
