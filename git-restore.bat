@echo off
echo =========================================
echo Git Bundle Restore Script
echo =========================================
echo This script will restore from Git bundle backups
echo =========================================
echo.

echo Available bundle files:
dir backup\*.bundle /b 2>nul
if errorlevel 1 (
    echo No bundle files found in backup directory
    pause
    exit /b 1
)
echo.

set /p BUNDLE_FILE="Enter bundle filename (with .bundle extension): "
if not exist "backup\%BUNDLE_FILE%" (
    echo Bundle file not found: backup\%BUNDLE_FILE%
    pause
    exit /b 1
)

echo Step 1: Verifying bundle...
git bundle verify "backup\%BUNDLE_FILE%"
if errorlevel 1 (
    echo [FAILED] Bundle verification failed
    pause
    exit /b 1
) else (
    echo [SUCCESS] Bundle verified
)
echo.

echo Step 2: Displaying bundle contents...
echo Available branches and tags:
git bundle list-heads "backup\%BUNDLE_FILE%"
echo.

echo Choose restore method:
echo 1. Clone to new directory
echo 2. Fetch into current repository
echo 3. Create new branch from bundle
set /p RESTORE_METHOD="Enter choice (1-3): "

if "%RESTORE_METHOD%"=="1" (
    set /p NEW_DIR="Enter new directory name: "
    echo Cloning bundle to new directory: !NEW_DIR!
    git clone "backup\%BUNDLE_FILE%" "!NEW_DIR!"
    if errorlevel 1 (
        echo [FAILED] Clone failed
    ) else (
        echo [SUCCESS] Repository cloned to !NEW_DIR!
        echo.
        echo Next steps:
        echo   cd !NEW_DIR!
        echo   git remote set-url origin ^<new-remote-url^>
        echo   git push -u origin --all
    )
) else if "%RESTORE_METHOD%"=="2" (
    echo Adding bundle as temporary remote...
    git remote add temp-bundle "backup\%BUNDLE_FILE%"
    git fetch temp-bundle
    echo.
    echo Available branches from bundle:
    git branch -r | findstr temp-bundle
    echo.
    set /p BRANCH_NAME="Enter branch name to merge (e.g., temp-bundle/main): "
    git merge "!BRANCH_NAME!"
    git remote remove temp-bundle
    echo [SUCCESS] Bundle content fetched and merged
) else if "%RESTORE_METHOD%"=="3" (
    echo Adding bundle as temporary remote...
    git remote add temp-bundle "backup\%BUNDLE_FILE%"
    git fetch temp-bundle
    echo.
    echo Available branches from bundle:
    git branch -r | findstr temp-bundle
    echo.
    set /p SOURCE_BRANCH="Enter source branch (e.g., temp-bundle/main): "
    set /p NEW_BRANCH="Enter new branch name: "
    git checkout -b "!NEW_BRANCH!" "!SOURCE_BRANCH!"
    git remote remove temp-bundle
    echo [SUCCESS] New branch !NEW_BRANCH! created from bundle
) else (
    echo Invalid choice
    pause
    exit /b 1
)

echo.
echo =========================================
echo Restore completed!
echo =========================================
pause
