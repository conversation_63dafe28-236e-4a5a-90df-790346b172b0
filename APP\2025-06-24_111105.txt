@echo off
set TI_CLANG_PATH=C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\bin
Set CPPCHECK_PATH=C:\Program Files\Cppcheck
set PATH=%PATH%;%TI_CLANG_PATH%;%CPPCHECK_PATH%
set PROJECT_PATH=.\dailyBuildConfigs
cd %PROJECT_PATH%

REM Run cppcheck with all checks enabled, focusing on security issues
REM --suppress=*:*cmsis* ^
cppcheck --project=compile_commands.json ^
    --enable—all ^
    --enable—warning,style,performance,portability,information ^
    --suppress—missinglnclude ^
    --suppress—missingInc1udeSystem ^
    --suppress=unusedFunction ^
    --clang=tiarmclang ^
    --template="{severity}: {message} [{id}]\n    {file}({line})" ^
    --suppress—*.*compiler* ^
    --output-file=cppcheck-result.txt

REM Check if cppcheck execution was successful
if %errorlevel% neq 0 (
    echo Cppcheck execution failed. Please verify cppcheck installation.
    exit /b 1
)
REM Check for error-level issues (including security errors)
findstr /I /C:"error:" cppcheck-result.txt > nul
if %errorlevel% equ 0 (
    echo [ERROR] Critical or security issues found!
    echo.
    REM Display only error-level issues
    findstr /I /C:"error:" cppcheck-result.txt
    echo.
    echo For complete analysis report, please check cppcheck-result.txt
    exit /b 1
) else (
    echo [PASS] Check completed. No critical errors found.
    echo.
    echo Warnings and other suggestions:
    type cppcheck-result.txt
    exit /b 0
)