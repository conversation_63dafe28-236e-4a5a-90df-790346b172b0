@echo off
setlocal enabledelayedexpansion
echo =========================================
echo Project Reorganization Script
echo =========================================
echo This script will reorganize the project structure
echo =========================================
echo.

pause

echo Step 1: Creating subdirectories...
if not exist MSPM0G3519_BMS_APP mkdir MSPM0G3519_BMS_APP
if not exist MSPM0G3519_BMS_FBL mkdir MSPM0G3519_BMS_FBL
echo - Directories created
echo.

echo Step 2: Moving files to MSPM0G3519_BMS_APP...
echo Moving visible files...

REM List of files to preserve at root
set PRESERVE_FILES=MSPM0G3519_BMS_APP MSPM0G3519_BMS_FBL dailyBuildConfigs .gitea .gitignore .git reorganize-project.bat reorganize-project-clean.bat reorganize-fixed.bat test-reorganize.bat git-backup.bat git-restore.bat

REM Move visible files
for %%f in (*) do (
    set SKIP=0
    for %%p in (%PRESERVE_FILES%) do (
        if "%%f"=="%%p" set SKIP=1
    )
    if !SKIP!==0 (
        echo Moving %%f
        git mv "%%f" MSPM0G3519_BMS_APP\ 2>nul
    )
)

echo Moving hidden files...
REM Move hidden files except preserved ones
for /f "delims=" %%f in ('dir /b /a:h .* 2^>nul') do (
    if not "%%f"==".gitea" (
        if not "%%f"==".gitignore" (
            if not "%%f"==".git" (
                echo Moving hidden file %%f
                git add -f "%%f" 2>nul
                git mv "%%f" MSPM0G3519_BMS_APP\ 2>nul
            )
        )
    )
)

echo - File moving completed
echo.

echo Step 3: Updating .gitignore...
echo # MSPM0G3519_BMS Application directory ignores > .gitignore
echo MSPM0G3519_BMS_APP/**/.settings/ >> .gitignore
echo MSPM0G3519_BMS_APP/Debug/ >> .gitignore
echo. >> .gitignore
echo # MSPM0G3519_BMS Flash Boot Loader directory ignores >> .gitignore
echo MSPM0G3519_BMS_FBL/**/.settings/ >> .gitignore
echo MSPM0G3519_BMS_FBL/Debug/ >> .gitignore
echo - .gitignore updated
echo.

echo Step 4: Staging changes...
git add .
echo - Changes staged
echo.

echo Step 5: Committing changes...
git commit -m "Reorganize project structure to subdirectories"
echo - Changes committed
echo.

echo =========================================
echo Reorganization completed!
echo =========================================
echo.
echo Final structure:
echo   Root/
echo   - .gitea/                    CI/CD workflows
echo   - dailyBuildConfigs/         Build scripts  
echo   - MSPM0G3519_BMS_APP/        Application files
echo   - MSPM0G3519_BMS_FBL/        Flash Boot Loader files
echo   - .gitignore                 Updated ignore rules
echo.
pause
