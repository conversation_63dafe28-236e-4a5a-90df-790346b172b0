@echo off
echo =========================================
echo Git Bundle Backup Script
echo =========================================
echo This script will create Git bundle backups
echo and optionally push to remote repositories
echo =========================================
echo.

REM Set backup directory and filename with timestamp
set BACKUP_DIR=backup
set TIMESTAMP=%date:~0,4%%date:~5,2%%date:~8,2%_%time:~0,2%%time:~3,2%%time:~6,2%
set TIMESTAMP=%TIMESTAMP: =0%
set BUNDLE_NAME=bq769x2_backup_%TIMESTAMP%.bundle

echo Creating backup directory...
mkdir %BACKUP_DIR% 2>nul

echo Step 1: Creating complete Git bundle backup...
git bundle create %BACKUP_DIR%\%BUNDLE_NAME% --all
if errorlevel 1 (
    echo [FAILED] Failed to create bundle
    pause
    exit /b 1
) else (
    echo [SUCCESS] Bundle created: %BACKUP_DIR%\%BUNDLE_NAME%
)
echo.

echo Step 2: Verifying bundle integrity...
git bundle verify %BACKUP_DIR%\%BUNDLE_NAME%
if errorlevel 1 (
    echo [FAILED] Bundle verification failed
    pause
    exit /b 1
) else (
    echo [SUCCESS] Bundle verified successfully
)
echo.

echo Step 3: Displaying bundle information...
echo Bundle contents:
git bundle list-heads %BACKUP_DIR%\%BUNDLE_NAME%
echo.

echo Step 4: Optional - Push to backup repository...
set /p PUSH_TO_BACKUP="Do you want to push to backup repository? (y/n): "
if /i "%PUSH_TO_BACKUP%"=="y" (
    echo.
    echo Available remotes:
    git remote -v
    echo.
    set /p BACKUP_REMOTE="Enter backup remote name (or 'skip' to skip): "
    if not "%BACKUP_REMOTE%"=="skip" (
        echo Pushing to %BACKUP_REMOTE%...
        git push %BACKUP_REMOTE% --all
        git push %BACKUP_REMOTE% --tags
        if errorlevel 1 (
            echo [WARNING] Push to %BACKUP_REMOTE% failed
        ) else (
            echo [SUCCESS] Pushed to %BACKUP_REMOTE%
        )
    )
)
echo.

echo Step 5: Copy bundle to external location...
set /p COPY_EXTERNAL="Do you want to copy bundle to external location? (y/n): "
if /i "%COPY_EXTERNAL%"=="y" (
    set /p EXTERNAL_PATH="Enter external path (e.g., D:\Backups\): "
    if not "%EXTERNAL_PATH%"=="" (
        copy "%BACKUP_DIR%\%BUNDLE_NAME%" "%EXTERNAL_PATH%"
        if errorlevel 1 (
            echo [FAILED] Failed to copy to external location
        ) else (
            echo [SUCCESS] Bundle copied to %EXTERNAL_PATH%
        )
    )
)
echo.

echo =========================================
echo Backup completed successfully!
echo =========================================
echo Bundle file: %BACKUP_DIR%\%BUNDLE_NAME%
echo Bundle size:
dir "%BACKUP_DIR%\%BUNDLE_NAME%" | findstr %BUNDLE_NAME%
echo.
echo To restore from this bundle:
echo   git clone %BUNDLE_NAME% restored-repo
echo   cd restored-repo
echo   git remote set-url origin ^<new-remote-url^>
echo.
pause
