@echo off
echo Starting reorganization test...

echo Step 1: Creating directories...
mkdir MSPM0G3519_BMS_APP 2>nul
mkdir MSPM0G3519_BMS_FBL 2>nul
echo Directories created.

echo Step 2: Testing file enumeration...
echo Visible files:
for %%f in (*) do (
    echo Found: %%f
)

echo Hidden files:
for /f "delims=" %%f in ('dir /b /a:h .* 2^>nul') do (
    echo Found hidden: %%f
)

echo Step 3: Testing git status...
git status --porcelain

echo Test completed.
pause
