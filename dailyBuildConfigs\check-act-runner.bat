@echo off
echo ========================================
echo Checking Gitea act_runner Status
echo ========================================
echo.

echo 1. Checking if act_runner process is running...
tasklist /FI "IMAGENAME eq act_runner.exe" 2>nul | find /I "act_runner.exe" >nul
if %errorlevel% equ 0 (
    echo [OK] act_runner.exe process is running
    echo.
    echo Process details:
    tasklist /FI "IMAGENAME eq act_runner.exe" /FO TABLE
) else (
    echo [WARNING] act_runner.exe process not found
    echo.
    echo Checking for alternative process names...
    tasklist /FI "IMAGENAME eq gitea-act-runner.exe" 2>nul | find /I "gitea-act-runner.exe" >nul
    if %errorlevel% equ 0 (
        echo [OK] gitea-act-runner.exe process is running
        tasklist /FI "IMAGENAME eq gitea-act-runner.exe" /FO TABLE
    ) else (
        echo [ERROR] No act_runner process found
    )
)

echo.
echo 2. Checking Windows Services...
sc query | find /I "act" >nul
if %errorlevel% equ 0 (
    echo [INFO] Found services containing 'act':
    sc query | find /I "act"
) else (
    echo [INFO] No services with 'act' in name found
)

echo.
echo 3. Checking common act_runner installation locations...
set "common_paths=C:\act_runner C:\gitea C:\Program Files\act_runner C:\Program Files\Gitea"

for %%p in (%common_paths%) do (
    if exist "%%p" (
        echo [FOUND] Directory: %%p
        if exist "%%p\act_runner.exe" (
            echo [OK] act_runner.exe found in %%p
        )
        if exist "%%p\gitea-act-runner.exe" (
            echo [OK] gitea-act-runner.exe found in %%p
        )
    )
)

echo.
echo 4. Checking network connections (act_runner typically uses port 3000 or 8080)...
netstat -an | find ":3000" >nul
if %errorlevel% equ 0 (
    echo [INFO] Port 3000 connections found:
    netstat -an | find ":3000"
)

netstat -an | find ":8080" >nul
if %errorlevel% equ 0 (
    echo [INFO] Port 8080 connections found:
    netstat -an | find ":8080"
)

echo.
echo 5. Checking recent Windows Event Logs for act_runner...
echo [INFO] Checking Application Event Log for recent errors...
wevtutil qe Application /c:10 /rd:true /f:text | find /I "act" 2>nul
if %errorlevel% neq 0 (
    echo [INFO] No recent act_runner related events found in Application log
)

echo.
echo 6. Checking if Gitea Actions is enabled...
echo [INFO] You can verify this by:
echo - Checking your Gitea configuration file (app.ini)
echo - Looking for [actions] section with ENABLED = true
echo - Checking Gitea admin panel under Site Administration

echo.
echo ========================================
echo Manual Verification Steps:
echo ========================================
echo.
echo A. Check Gitea Web Interface:
echo    1. Go to your Gitea instance
echo    2. Navigate to Site Administration ^> Actions ^> Runners
echo    3. Verify runners are listed and online
echo.
echo B. Check Runner Logs:
echo    1. Find your act_runner installation directory
echo    2. Look for log files (usually .log extension)
echo    3. Check for recent activity and errors
echo.
echo C. Test Runner Manually:
echo    1. Navigate to act_runner directory
echo    2. Run: act_runner.exe --help
echo    3. Check configuration with: act_runner.exe daemon --config config.yaml
echo.
echo D. Trigger a Test Workflow:
echo    1. Go to your repository Actions tab
echo    2. Find a workflow with "workflow_dispatch" trigger
echo    3. Click "Run workflow" button
echo    4. Monitor execution status
echo.
echo ========================================
echo Common Issues and Solutions:
echo ========================================
echo.
echo Issue 1: Runner not registered
echo Solution: Re-register runner with Gitea instance
echo Command: act_runner.exe register --instance http://your-gitea-url --token YOUR_TOKEN
echo.
echo Issue 2: Runner offline
echo Solution: Restart runner service or process
echo Command: act_runner.exe daemon --config config.yaml
echo.
echo Issue 3: Permission issues
echo Solution: Run as administrator or check file permissions
echo.
echo Issue 4: Network connectivity
echo Solution: Check firewall, proxy settings, and network access to Gitea
echo.
echo ========================================
echo Check completed!
echo ========================================
