<?xml version="1.0" encoding="UTF-8" ?>
<?ccsproject version="1.0"?>
<projectOptions>
	<ccsVariant value="50:Theia-based"/>
	<ccsVersion value="70.2.0"/>
	<deviceFamily value="TMS470"/>
	<connection value="common/targetdb/connections/TIXDS110_Connection.xml"/>
	<executableActions value=""/>
	<createSlaveProjects value=""/>
	<ignoreDefaultDeviceSettings value="true"/>
	<ignoreDefaultCCSSettings value="true"/>
	<templateProperties value="id=bq769x2_TIDA010247_LP_MSPM0G3519_nortos_ticlang.projectspec.bq769x2_TIDA010247_LP_MSPM0G3519_nortos_ticlang,buildProfile=release,isHybrid=true"/>
	<activeTargetConfiguration value="targetConfigs/MSPM0G3519.ccxml"/>
	<isTargetConfigurationManual value="false"/>
	<sourceLookupPath value="${COM_TI_MSPM0_SDK_INSTALL_DIR}/source/ti/driverlib"/>
	<origin value="C:\ti\mspm0_sdk_2_05_00_05\examples\nortos\LP_MSPM0G3519\demos\bq769x2_TIDA010247\ticlang\bq769x2_TIDA010247_LP_MSPM0G3519_nortos_ticlang.projectspec"/>
	<filesToOpen value="README.md,bq769x2_TIDA010247.syscfg"/>
</projectOptions>
