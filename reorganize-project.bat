@echo off
echo =========================================
echo Project Reorganization Script
echo =========================================
echo This script will:
echo 1. Create APP and Bootloader subdirectories
echo 2. Move ALL files and directories to APP/ (except preserved ones)
echo 3. Preserve dailyBuildConfigs, .gitea, .gitignore at root level
echo 4. Update .gitignore with directory prefixes
echo 5. Preserve all commit history
echo 6. Use general rules instead of specific file names
echo =========================================
echo.

pause

echo Step 1: Creating subdirectories...
mkdir APP 2>nul
mkdir Bootloader 2>nul
echo - APP directory created
echo - Bootloader directory created
echo.

echo Step 2: Moving all files and directories to APP/ (excluding preserved directories)...

REM Move all visible files (non-hidden) to APP/
for %%f in (*) do (
    if not "%%f"=="APP" (
        if not "%%f"=="Bootloader" (
            if not "%%f"=="dailyBuildConfigs" (
                if not "%%f"=="reorganize-project.bat" (
                    echo Moving %%f to APP/
                    git mv "%%f" APP/ 2>nul
                )
            )
        )
    )
)

REM Move all hidden files and directories (starting with .) to APP/ except .gitea and .gitignore
for /f "delims=" %%f in ('dir /b /a:h .* 2^>nul ^| findstr /v "^\.gitea$ ^\.gitignore$ ^\.git$"') do (
    echo Moving hidden file/directory %%f to APP/
    git add -f "%%f" 2>nul
    git mv "%%f" APP/ 2>nul
)

echo - All applicable files and directories moved to APP/
echo.

echo.
echo Step 3: Updating .gitignore with directory prefixes...
echo # APP directory ignores > .gitignore
echo APP/**/.settings/ >> .gitignore
echo APP/Debug/ >> .gitignore
echo. >> .gitignore
echo # Bootloader directory ignores >> .gitignore
echo Bootloader/**/.settings/ >> .gitignore
echo Bootloader/Debug/ >> .gitignore
echo - .gitignore updated with directory prefixes
echo.

echo Step 4: Adding all changes to git...
git add .
echo - All changes staged
echo.

echo Step 5: Committing changes...
git commit -m "Reorganize: Move all files to APP subdirectory using general rules

- Move all files and directories to APP/ using pattern matching
- Preserve dailyBuildConfigs, .gitea, .gitignore at root level
- Preserve complete commit history for all moved files
- Update .gitignore with directory-specific patterns
- Create Bootloader directory for future use
- Use general file handling instead of specific file names"
echo - Changes committed with preserved history
echo.

echo =========================================
echo Project reorganization completed successfully!
echo =========================================
echo.
echo Final structure:
echo   Root/
echo   ├── .gitea/                 (CI/CD workflows)
echo   ├── dailyBuildConfigs/      (Build scripts)
echo   ├── APP/                    (Application files)
echo   ├── Bootloader/             (Ready for bootloader files)
echo   └── .gitignore              (Updated with prefixes)
echo.
echo All commit history has been preserved.
echo You can now add bootloader files to the Bootloader/ directory.
echo.
pause
