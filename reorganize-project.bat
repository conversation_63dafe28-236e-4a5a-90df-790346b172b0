@echo off
echo =========================================
echo Project Reorganization Script
echo =========================================
echo This script will:
echo 1. Create APP and Bootloader subdirectories
echo 2. Move all application files to APP/ directory
echo 3. Move all project configuration files to APP/
echo 4. Update .gitignore with directory prefixes
echo 5. Preserve all commit history
echo 6. Keep dailyBuildConfigs and .gitea at root level
echo =========================================
echo.

pause

echo Step 1: Creating subdirectories...
mkdir APP 2>nul
mkdir Bootloader 2>nul
echo - APP directory created
echo - Bootloader directory created
echo.

echo Step 2: Moving application source files to APP/...
git mv App_task.c APP/ 2>nul
git mv App_task.h APP/ 2>nul
git mv BQ769x2_Configs APP/ 2>nul
git mv Communications APP/ 2>nul
git mv Debug APP/ 2>nul
git mv README.html APP/ 2>nul
git mv README.md APP/ 2>nul
git mv authentication-troubleshooting.md APP/ 2>nul
git mv bq769x2_TIDA010247.syscfg APP/ 2>nul
git mv main.c APP/ 2>nul
git mv targetConfigs APP/ 2>nul
git mv 2025-06-24_111105.txt APP/ 2>nul
echo - Application source files moved to APP/
echo.

echo Step 3: Moving project configuration files to APP/...
git mv .ccsproject APP/ 2>nul
git mv .clangd APP/ 2>nul
git mv .cproject APP/ 2>nul
git mv .project APP/ 2>nul
git add -f .settings 2>nul
git mv .settings APP/ 2>nul
echo - Project configuration files moved to APP/
echo.

echo Step 4: Updating .gitignore with directory prefixes...
echo # APP directory ignores > .gitignore
echo APP/**/.settings/ >> .gitignore
echo APP/Debug/ >> .gitignore
echo. >> .gitignore
echo # Bootloader directory ignores >> .gitignore
echo Bootloader/**/.settings/ >> .gitignore
echo Bootloader/Debug/ >> .gitignore
echo - .gitignore updated with directory prefixes
echo.

echo Step 5: Adding all changes to git...
git add .
echo - All changes staged
echo.

echo Step 6: Committing changes...
git commit -m "Reorganize: Move all files to APP subdirectory

- Move all application source files to APP/ directory
- Move all project configuration files to APP/
- Preserve complete commit history for all moved files
- Keep dailyBuildConfigs and .gitea at root level for CI/CD
- Update .gitignore with directory-specific patterns
- Create Bootloader directory for future use"
echo - Changes committed with preserved history
echo.

echo =========================================
echo Project reorganization completed successfully!
echo =========================================
echo.
echo Final structure:
echo   Root/
echo   ├── .gitea/                 (CI/CD workflows)
echo   ├── dailyBuildConfigs/      (Build scripts)
echo   ├── APP/                    (Application files)
echo   ├── Bootloader/             (Ready for bootloader files)
echo   └── .gitignore              (Updated with prefixes)
echo.
echo All commit history has been preserved.
echo You can now add bootloader files to the Bootloader/ directory.
echo.
pause
